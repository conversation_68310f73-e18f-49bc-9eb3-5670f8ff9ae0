-- <PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
     
  minimum = 150
  limit = 550
  
  a = 75            --kenardan mesafe (kapak kenarından pencere islemlerinin basladığı kenara mesafe)
  K = 100           --sol ust pencere genisligi
  L = 150           --sol ust pencere yuksekligi
  C = 8             --cita genisligi
  
  K1Off = 5              -- <PERSON><PERSON><PERSON> Offset
  K1Opr = 6              --Yapilacak islem 1-<PERSON>_<PERSON><PERSON><PERSON>, 2-<PERSON>_<PERSON><PERSON>, 3-<PERSON>_BalikSirti, 4-<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5-<PERSON>_<PERSON><PERSON><PERSON><PERSON>, 6-<PERSON><PERSON><PERSON>
  K1Depth = 2           -- Kanal Derinligi
  K1DiaAngle = 89.1 				-- Cap veya Vbit Acisi
  
  K2Off = 10              -- <PERSON><PERSON><PERSON> Offset
  K2Opr = 6               --<PERSON><PERSON><PERSON><PERSON>k islem 1-<PERSON>_<PERSON><PERSON><PERSON>, 2-<PERSON><PERSON><PERSON><PERSON>, 3-<PERSON>_<PERSON><PERSON><PERSON><PERSON>, 4-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,5-<PERSON>_<PERSON><PERSON><PERSON>, 6-<PERSON><PERSON><PERSON>
  K2Depth = 2             -- Kanal Derinligi
  K2DiaAngle = 50 			-- Cap veya Vbit Acisi
  
  K3Off = 15              -- Kenardan Offset
  K3Opr = 2               --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K3Depth = 2             -- Kanal Derinligi
  K3DiaAngle = 50 			-- Cap veya Vbit Acisi
  
  K4Off = 20              -- Kenardan Offset
  K4Opr = 6               --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K4Depth = 2             -- Kanal Derinligi
  K4DiaAngle = 50 			-- Cap veya Vbit Acisi
  
  CC_FirstToolDia = 20			--kullanılan ilk takim capı
  CC_CleanerToolDia = 5			-- bu islemde kullanılan takım capi (daha kucuk olmalı)
  CC_Off = 10					--kenardan offset
  CC_Dep = 1					-- islem derinligi
  
  KLineAvailable = 0
  KLineRotation = 1   --yatay:0, dikey:1
  KLineOffset = 60
  KLineDepth = 1.4
  
  KBcOff = 0              -- Kenardan Offset
	KBcOpr = 1				--Arka yuzey kanallar var mı 1 yok mu 0
	KBcDepth = 7.5           -- Kanal Derinligi
	KBcDia	 = 20 				-- Cap
	
  
  extEdgeVtoolExist       	= 5   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local M = X-2(K+C+a)
  local N = Y-(L+C)+2*a
  
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  function Decimals(W)
    W = string.gsub(W,"[.]","_")
    return W
  end
  
  local bulge = math.tan(math.pi/8)
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
    --Operasyonlardan aktif olanların işlemelerinin yapılması
  function Operasyonlar(x1,y1,x2,y2,opr,off,dep,W)
    local sunkenAngle = W
		local sunkenWidth = dep * math.tan((math.pi*W/180)/2.0)
		local vMidDiameter = 100
		G.setThickness(-dep)
    W=Decimals(W)
		local point1 = {x1+off, y1+off}
		local point2 = {x2-off, y2-off}
    
    if opr ==1 then
			G.setLayer("K_Freze"..W.."mm")		
			G.rectangle(point1,point2)
		end	
		if opr ==2 then
			G.setLayer("K_Ballnose"..W.."mm")	
			G.rectangle(point1,point2)
		end
		if opr ==3 then
			G.setLayer("K_BalikSirti"..W.."mm")		
			G.rectangle(point1,point2)
		end
		if opr ==4 then
			G.setLayer("Cep_Acma")		
			G.rectangle(point1,point2)
		end
		if opr ==5 then
			G.setLayer("K_AciliV"..W)
			G.rectangle(point1,point2)			
		end
		if opr ==6 then
			G.setLayer("K_AciliV"..W)	
			G.setThickness(0)			
			G.sunkenFrame(point1, point2, dep, sunkenAngle, vMidDiameter)
		end
		if opr ==7 then
			G.setLayer("K_Desen"..W)		
			G.rectangle(point1,point2)
		end
  end
	
  --Operasyonlardan hangileri varsa uygulatma ayarları
	function Kanallar(x1,y1,x2,y2,K1Opr,K2Opr,K3Opr,K4Opr)
    if K1Opr ~= 0 then
      Operasyonlar(x1,y1,x2,y2,K1Opr,K1Off,K1Depth,K1DiaAngle)
    end
    if K2Opr ~= 0 then
        Operasyonlar(x1,y1,x2,y2,K2Opr,K2Off,K2Depth,K2DiaAngle)
    end
    if K3Opr ~= 0 then
        Operasyonlar(x1,y1,x2,y2,K3Opr,K3Off,K3Depth,K3DiaAngle)
    end
    if K4Opr ~= 0 then
        Operasyonlar(x1,y1,x2,y2,K4Opr,K4Off,K4Depth,K4DiaAngle)
    end
	end
  
	if KLineAvailable == 1 then
		G.setLayer("K_Cizgi")	
		G.setThickness(-KLineDepth)
		if KLineRotation == 0 then
		G.line({0, KLineOffset}, {X, KLineOffset})
		G.line({0, Y - KLineOffset}, {X, Y-KLineOffset})
		end
		if KLineRotation == 1 then
		G.line({KLineOffset, 0},{KLineOffset, Y})
		G.line({X-KLineOffset, 0}, {X-KLineOffset, Y})
		end
	end
  
  point1 = {a,a}
  point2 = {a+K,a+N}
  point3 = {a,a+N+C}
  point4 = {a+K,Y-a}
  point5 = {a+K+C,a}
  point6 = {a+K+C+M,a+N}
  point7 = {a+K+C,a+N+C}
  point8 = {a+K+C+M,Y-a}
  point9 =  {a+K+C+M+C,a}
  point10 = {X-a,a+N}
  point11 = {a+K+C+M+C,a+N+C}
  point12 = {X-a,Y-a}

  -- Döngüyle kanal fonksiyonlarını çağır
  for a, pair in ipairs(pointPairs) do
    
    p1 = pair[1]
    p2 = pair[2]
    Kanallar (p1[1],p1[2],p2[1],p2[2],K1Opr,K2Opr,K3Opr,K4Opr)
    
    --kose temizleme islemleri var mı?
    if CC_CleanerToolDia ~= 0 then
      G.setLayer("Cep_KoseTemizle"..Decimals(CC_CleanerToolDia))	
      G.setThickness(-CC_Dep)
      G.cleanCorners2({p1[1]+CC_Off,p1[2]+CC_Off}, {p2[1]-CC_Off, p2[2]-CC_Off}, CC_FirstToolDia, CC_CleanerToolDia)
    end
  --kose temizleme bitis
  end

  ----arka yuzeyde cerceve ustu islemler 
	G.setFace("bottom")
	if KBcOpr >0 then  ----arka yuzeyde cerceve ustu islemler 
    KBcDia = Decimals(KBcDia)
		G.setLayer("K_Freze"..KBcDia.."mm_SF")		--Clearing procedures of the backside
		for i = 1, stepX do
			G.setThickness(-KBcDepth)
			local b = K1Off-KBcOff
			local a = K1Off+i*correctedblockX+((i*2)-1)*(c/2)
			point1 = {a,b}
			point2 = {a,Y-b}
			G.line(point1,point2,0)
		end
		
		for j = 1, stepY do
			G.setThickness(-KBcDepth)
			local a = K1Off-KBcOff
			-- local y = KBcOff+j*correctedblockY+((j*2)-1)*(c/2)
			local b = K1Off+j*correctedblockY+ ((j*2)-1)*(c/2)
			--print(b)
			point1 = {a,b}
			point2 = {X-a,b}
			G.line(point1,point2,0)
		end
		G.setLayer("K_Freze"..KBcDia.."mm_SF")
		G.setThickness(-KBcDepth)
		G.rectangle({K1Off-KBcOff, K1Off-KBcOff}, {X-K1Off+KBcOff, Y-K1Off+KBcOff})
	end
	
  return true
end

------------------------------------------------

require "ADekoDebugMode"
