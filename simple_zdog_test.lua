-- Simple test for Zdog 3D visualization
-- This creates just a few basic shapes to verify the system works

function modelMain()
    G = ADekoLib
    
    print("Creating simple test shapes...")
    
    -- Simple rectangle at origin
    G.setLayer("TEST")
    G.setThickness(-10)
    G.rectangle({0, 0}, {100, 100})
    
    -- Simple circle nearby
    <PERSON><PERSON>set<PERSON>ayer("CIRCLE_TEST")
    G.setThickness(-15)
    G.circle({150, 50}, 30)
    
    -- Simple line
    G.setLayer("LINE_TEST")
    G.setThickness(-5)
    G.line({50, 150}, {150, 200})
    
    print("Simple test completed - check 3D view!")
end

-- Run the test
require "ADekoDebugMode"
