<template>
  <div class="zdog-canvas-container">
    <canvas
      ref="zdogCanvasRef"
      class="zdog-canvas"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
    ></canvas>
    
    <!-- Controls overlay -->
    <div class="controls-overlay">
      <div class="control-group">
        <button @click="resetView" class="control-btn" title="Reset View">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
            <path d="M3 3v5h5"/>
          </svg>
        </button>
        <button @click="toggle3D" class="control-btn" :class="{ active: is3DMode }" title="Toggle 3D Mode">
          3D
        </button>
        <button @click="toggleWireframe" class="control-btn" :class="{ active: wireframe }" title="Toggle Wireframe">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7v10l10 5 10-5V7z"/>
            <path d="M2 7l10 5"/>
            <path d="M12 12v10"/>
            <path d="M22 7l-10 5"/>
          </svg>
        </button>
      </div>
      
      <div class="info-panel">
        <div class="info-item">Shapes: {{ shapeCount }}</div>
        <div class="info-item">Mode: {{ is3DMode ? '3D' : '2D' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import * as Zdog from 'zdog'
import type { DrawCommand } from '@/types'

// Debug: Check if Zdog is imported correctly
console.log('Zdog imported:', Zdog)
console.log('Zdog.Illustration:', Zdog.Illustration)
console.log('Zdog.Shape:', Zdog.Shape)
console.log('Zdog.Group:', Zdog.Group)

interface Props {
  drawCommands: DrawCommand[]
}

const props = defineProps<Props>()

const zdogCanvasRef = ref<HTMLCanvasElement>()
const is3DMode = ref(true)
const wireframe = ref(false)
const isDragging = ref(false)
const lastMouseX = ref(0)
const lastMouseY = ref(0)

let illo: Zdog.Illustration | null = null
let rootGroup: Zdog.Group | null = null

const shapeCount = computed(() => props.drawCommands.length)

const initZdog = () => {
  if (!zdogCanvasRef.value) {
    console.error('Canvas ref not available!')
    return
  }

  // Ensure canvas has proper dimensions
  const canvas = zdogCanvasRef.value
  const rect = canvas.parentElement?.getBoundingClientRect()
  if (rect && rect.width > 0 && rect.height > 0) {
    canvas.width = rect.width
    canvas.height = rect.height
  } else {
    // Fallback dimensions
    canvas.width = 800
    canvas.height = 600
  }

  console.log('Initializing Zdog with canvas:', canvas.width, 'x', canvas.height)

  // Test: Draw directly on canvas to verify it's working
  const ctx = canvas.getContext('2d')!
  ctx.fillStyle = '#ff0000'
  ctx.fillRect(100, 100, 100, 100)
  ctx.strokeStyle = '#0000ff'
  ctx.lineWidth = 5
  ctx.beginPath()
  ctx.moveTo(0, 0)
  ctx.lineTo(200, 200)
  ctx.stroke()
  console.log('Drew test shapes directly on canvas')

  // Destroy existing illustration if it exists
  if (illo) {
    illo = null
  }

  try {
    console.log('Creating Zdog Illustration with canvas:', canvas)
    console.log('Canvas context available:', !!canvas.getContext('2d'))

    // Create Zdog illustration
    illo = new Zdog.Illustration({
      element: canvas,
      zoom: 1,
      dragRotate: false, // We handle drag manually
      resize: false, // We handle resize manually
    })

    console.log('Zdog Illustration created:', illo)
    console.log('Zdog element:', illo.element)

    // Create root group for all shapes
    rootGroup = new Zdog.Group({
      addTo: illo,
    })

    console.log('Root group created:', rootGroup)

    // Set initial rotation
    illo.rotate = { x: 0, y: 0 }

    // Test: Create a simple test shape to verify Zdog is working
    const testShape = new Zdog.Shape({
      addTo: illo,
      path: [
        { x: -50, y: -50 },
        { x: 50, y: 50 }
      ],
      stroke: 10,
      color: '#00ff00'
    })
    console.log('Test shape created:', testShape)

    // Initial render
    illo.updateRenderGraph()
    console.log('Zdog initialization completed successfully!')

  } catch (error) {
    console.error('Error initializing Zdog:', error)
    if (error instanceof Error) {
      console.error('Error details:', error.message, error.stack)
    }
  }
}

const createZdogShapes = () => {
  console.log('createZdogShapes called with', props.drawCommands.length, 'commands')

  if (!illo || !rootGroup) {
    console.warn('Zdog not initialized yet, skipping shape creation')
    return
  }

  // Clear existing shapes
  rootGroup.children = []

  if (props.drawCommands.length === 0) {
    console.log('No draw commands, rendering empty scene')
    illo.updateRenderGraph()
    return
  }

  console.log('Creating shapes from draw commands...')
  let shapeCount = 0
  let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity

  props.drawCommands.forEach((command, index) => {
    try {
      console.log(`Processing command ${index + 1}/${props.drawCommands.length}:`, command.command_type)

      // Track bounding box
      minX = Math.min(minX, command.x1, command.x2)
      maxX = Math.max(maxX, command.x1, command.x2)
      minY = Math.min(minY, command.y1, command.y2)
      maxY = Math.max(maxY, command.y1, command.y2)

      switch (command.command_type) {
        case 'line':
          const lineStroke = Math.max(command.size || 2, 8) // Thicker stroke for visibility
          const lineColor = command.color || '#ff0000' // Bright red for visibility

          console.log(`  Creating line with stroke: ${lineStroke}, color: ${lineColor}`)

          // Create line using Zdog.Shape
          const lineShape = new Zdog.Shape({
            addTo: rootGroup!,
            path: [
              { x: command.x1, y: command.y1, z: is3DMode.value ? (command.z1 || 0) : 0 },
              { x: command.x2, y: command.y2, z: is3DMode.value ? (command.z2 || 0) : 0 }
            ],
            stroke: lineStroke,
            color: lineColor,
            fill: false
          })

          console.log(`  Line shape created:`, lineShape)

          shapeCount++
          console.log(`  Line created: (${command.x1},${command.y1}) to (${command.x2},${command.y2})`)
          break

        case 'circle':
          new Zdog.Ellipse({
            addTo: rootGroup!,
            diameter: command.radius * 2,
            translate: {
              x: command.x1,
              y: command.y1,
              z: is3DMode.value ? (command.z1 || 0) : 0
            },
            stroke: wireframe.value ? (command.size || 2) : undefined,
            fill: !wireframe.value,
            color: command.color || '#333'
          })
          shapeCount++
          console.log(`  Circle created: center (${command.x1},${command.y1}), radius ${command.radius}`)
          break

        case 'arc':
          // For arcs, we'll create a path with curved segments
          const startAngle = (command.start_angle || 0) * Math.PI / 180
          const endAngle = (command.end_angle || 360) * Math.PI / 180
          const arcRadius = command.radius
          const centerX = command.x1
          const centerY = command.y1
          
          // Create arc path points
          const arcSteps = 16
          const angleStep = (endAngle - startAngle) / arcSteps
          const arcPath: any[] = []
          
          for (let i = 0; i <= arcSteps; i++) {
            const angle = startAngle + (i * angleStep)
            arcPath.push({
              x: centerX + Math.cos(angle) * arcRadius,
              y: centerY + Math.sin(angle) * arcRadius,
              z: is3DMode.value ? (command.z1 || 0) : 0
            })
          }
          
          new Zdog.Shape({
            addTo: rootGroup!,
            path: arcPath,
            stroke: command.size || 2,
            color: command.color || '#333',
            fill: false
          })
          shapeCount++
          console.log(`  Arc created: center (${centerX},${centerY}), radius ${arcRadius}, angles ${command.start_angle}° to ${command.end_angle}°`)
          break

        case 'rectangle':
          // Calculate rectangle corners from center and size
          const width = Math.abs(command.x2 - command.x1)
          const height = Math.abs(command.y2 - command.y1)
          const rectCenterX = (command.x1 + command.x2) / 2
          const rectCenterY = (command.y1 + command.y2) / 2
          
          new Zdog.Box({
            addTo: rootGroup!,
            width: width,
            height: height,
            depth: is3DMode.value ? (command.thickness || 2) : 0,
            translate: { 
              x: rectCenterX, 
              y: rectCenterY, 
              z: is3DMode.value ? (command.z1 || 0) : 0 
            },
            stroke: wireframe.value ? (command.size || 2) : undefined,
            fill: !wireframe.value,
            color: command.color || '#333'
          })
          shapeCount++
          console.log(`  Rectangle created: center (${rectCenterX},${rectCenterY}), size ${width}x${height}`)
          break

        case 'polyline':
        case 'polygon':
          if (command.points && command.points.length > 1) {
            const polyPath = command.points.map(point => ({
              x: point[0],
              y: point[1],
              z: is3DMode.value ? (point[2] || 0) : 0
            }))
            
            // Close the path for polygons
            if (command.command_type === 'polygon' && command.points.length > 2) {
              polyPath.push(polyPath[0])
            }
            
            new Zdog.Shape({
              addTo: rootGroup!,
              path: polyPath,
              stroke: command.size || 2,
              color: command.color || '#333',
              fill: command.command_type === 'polygon' && !wireframe.value,
              closed: command.command_type === 'polygon'
            })
            shapeCount++
            console.log(`  ${command.command_type} created with ${command.points.length} points`)
          }
          break

        default:
          console.warn(`  Unknown command type: ${command.command_type}`)
      }
    } catch (error) {
      console.error(`Error creating shape for command ${index}:`, error)
    }
  })

  console.log(`Bounding box: x(${minX} to ${maxX}), y(${minY} to ${maxY})`)

  // Calculate center and scale to fit viewport
  const centerX = (minX + maxX) / 2
  const centerY = (minY + maxY) / 2
  const width = maxX - minX
  const height = maxY - minY

  console.log(`Content center: (${centerX}, ${centerY})`)
  console.log(`Content size: ${width} x ${height}`)

  // TEMPORARY: Skip coordinate transformation for debugging
  console.log('Skipping coordinate transformation for debugging')
  illo.zoom = 0.5 // Fixed zoom for testing
  rootGroup.translate = { x: 0, y: 0, z: 0 } // No translation for testing

  // Also create a simple test shape at origin for comparison
  const debugShape = new Zdog.Shape({
    addTo: rootGroup!,
    path: [
      { x: -100, y: 0 },
      { x: 100, y: 0 }
    ],
    stroke: 20,
    color: '#ff00ff' // Magenta for visibility
  })
  console.log('Debug shape created at origin:', debugShape)

  console.log(`Shape creation completed: ${shapeCount} shapes created`)

  // Reset rotation to ensure shapes are visible
  if (illo.rotate && (
      (illo.rotate.x && Math.abs(illo.rotate.x) > Math.PI) ||
      (illo.rotate.y && Math.abs(illo.rotate.y) > Math.PI))) {
    illo.rotate = { x: 0, y: 0 }
  }

  // Render the scene
  console.log('About to render with zoom:', illo.zoom, 'rotation:', illo.rotate)
  console.log('Root group children:', rootGroup.children.length)
  console.log('Root group translate:', rootGroup.translate)

  illo.updateRenderGraph()

  // Check if anything was actually drawn
  const canvas = zdogCanvasRef.value!
  const ctx = canvas.getContext('2d')!
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  let nonTransparentPixels = 0
  for (let i = 3; i < imageData.data.length; i += 4) {
    if (imageData.data[i] > 0) nonTransparentPixels++
  }
  console.log(`Canvas has ${nonTransparentPixels} non-transparent pixels out of ${canvas.width * canvas.height}`)

  // Draw a test shape directly to verify canvas is working
  ctx.fillStyle = '#00ff00'
  ctx.fillRect(10, 10, 50, 50)
  console.log('Drew green test rectangle at (10,10)')

  console.log('Zdog render completed')
}

const resetView = () => {
  if (illo) {
    illo.rotate = { x: -0.3, y: 0.3 }
    illo.zoom = 1
    illo.updateRenderGraph()
  }
}

const toggle3D = () => {
  is3DMode.value = !is3DMode.value
  createZdogShapes()
}

const toggleWireframe = () => {
  wireframe.value = !wireframe.value
  createZdogShapes()
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  if (illo) {
    const delta = event.deltaY > 0 ? 0.9 : 1.1
    illo.zoom = Math.max(0.1, Math.min(5, illo.zoom * delta))
    illo.updateRenderGraph()
  }
}

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !illo) return
  
  const deltaX = event.clientX - lastMouseX.value
  const deltaY = event.clientY - lastMouseY.value
  
  // Manual rotation control
  if (illo.rotate) {
    illo.rotate.y = (illo.rotate.y || 0) + deltaX * 0.01
    illo.rotate.x = (illo.rotate.x || 0) + deltaY * 0.01
  }
  
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
  
  illo.updateRenderGraph()
}

const handleMouseUp = () => {
  isDragging.value = false
}

// Watch for draw commands changes
watch(() => props.drawCommands, () => {
  console.log('Draw commands changed, scheduling shape creation...')
  nextTick(() => {
    createZdogShapes()
  })
}, { immediate: false })

// Watch for 3D mode changes
watch(is3DMode, () => {
  console.log('3D mode changed to:', is3DMode.value)
  createZdogShapes()
}, { flush: 'post' })

// Expose functions for parent component
defineExpose({
  resetView,
  toggle3D,
  toggleWireframe
})

// Initialize on mount
onMounted(() => {
  console.log('ZdogCanvas mounted, starting initialization...')
  
  nextTick(() => {
    console.log('In nextTick, canvas ref:', zdogCanvasRef.value)
    
    // Ensure canvas has proper size before initializing Zdog
    if (zdogCanvasRef.value && zdogCanvasRef.value.parentElement) {
      const rect = zdogCanvasRef.value.parentElement.getBoundingClientRect()
      console.log('Parent element rect:', rect)
      
      // Set canvas attributes for proper Zdog initialization
      if (rect.width > 0 && rect.height > 0) {
        zdogCanvasRef.value.width = rect.width
        zdogCanvasRef.value.height = rect.height
        // Also set style to match
        zdogCanvasRef.value.style.width = rect.width + 'px'
        zdogCanvasRef.value.style.height = rect.height + 'px'
        console.log('Canvas sized to:', rect.width, 'x', rect.height)
      } else {
        // Fallback if parent has no size yet
        zdogCanvasRef.value.width = 800
        zdogCanvasRef.value.height = 600
        zdogCanvasRef.value.style.width = '800px'
        zdogCanvasRef.value.style.height = '600px'
        console.log('Using fallback canvas size: 800x600')
      }
    }
    
    // Small delay to ensure DOM is fully ready
    setTimeout(() => {
      console.log('Starting Zdog initialization after delay...')
      initZdog()
      
      // Another small delay before creating shapes
      setTimeout(() => {
        console.log('Creating shapes after initialization...')
        createZdogShapes()
      }, 50)
    }, 100)

    // Handle window resize
    const resizeObserver = new ResizeObserver(() => {
      if (illo && zdogCanvasRef.value) {
        const rect = zdogCanvasRef.value.parentElement?.getBoundingClientRect()
        if (rect && rect.width > 0 && rect.height > 0) {
          zdogCanvasRef.value.width = rect.width
          zdogCanvasRef.value.height = rect.height
          zdogCanvasRef.value.style.width = rect.width + 'px'
          zdogCanvasRef.value.style.height = rect.height + 'px'
          illo.setSize(rect.width, rect.height)
          illo.updateRenderGraph()
          console.log('Canvas resized to:', rect.width, 'x', rect.height)
        }
      }
    })

    if (zdogCanvasRef.value?.parentElement) {
      resizeObserver.observe(zdogCanvasRef.value.parentElement)
    }
  })
})
</script>

<style scoped>
.zdog-canvas-container {
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f9fafb;
}

.zdog-canvas {
  width: 100%;
  height: 100%;
  cursor: grab;
  background-color: #ffffff; /* White background */
  border: 2px solid #ff0000; /* Debug border to verify canvas position */
  box-sizing: border-box;
}

.zdog-canvas:active {
  cursor: grabbing;
}

.controls-overlay {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: none;
}

.control-group {
  display: flex;
  gap: 0.25rem;
  pointer-events: auto;
}

.control-btn {
  padding: 0.25rem 0.5rem;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  cursor: pointer;
}

.control-btn:hover {
  background-color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.control-btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.info-panel {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  pointer-events: auto;
}

.info-item {
  color: #4b5563;
}
</style>
