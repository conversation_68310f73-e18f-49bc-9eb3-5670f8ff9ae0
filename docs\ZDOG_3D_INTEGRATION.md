# Zdog 3D Visualization Integration

This document explains the new Zdog-based pseudo 3D visualization feature added to the Lua Editor.

## Overview

Zdog is a pseudo-3D engine for canvas and SVG that provides smooth, crisp pseudo-3D graphics. We've integrated it into the visualization panel to offer a 3D perspective of your Lua-generated shapes and drawings.

## Features

### 3D View Mode
- **Access**: Click the "3D" tab in the visualization panel or press `Ctrl+2`
- **Interactive**: Drag to rotate the view, scroll to zoom
- **Real-time**: Automatically updates when your Lua script generates new drawing commands

### Supported 3D Shapes
1. **3D Boxes**: Rectangles are rendered as extruded boxes with depth
2. **3D Cylinders**: Circles become cylinders with configurable height
3. **3D Lines**: Lines can have different Z coordinates for start and end points
4. **3D Arcs**: Arcs are rendered with smooth curves
5. **3D Polygons**: Complex polygons are extruded with side faces
6. **Layer Depths**: Different layers can have different extrusion depths

### Controls
- **Reset View**: Click the reset button or use the reset function
- **3D Mode Toggle**: Switch between 2D flat view and full 3D extrusion
- **Wireframe Toggle**: Switch between solid shapes and wireframe display
- **Drag Rotation**: Click and drag to rotate the 3D scene
- **Zoom**: Use mouse wheel to zoom in/out

## Usage Examples

### Basic 3D Shapes
```lua
function modelMain()
    G = ADekoLib
    
    -- Create a 3D box (rectangle with depth from thickness)
    G.setLayer("BOX_LAYER")
    G.setThickness(-30)  -- Negative value indicates cutting depth
    G.rectangle({50, 50}, {250, 200})
    
    -- Create a 3D cylinder (circle with height from thickness)
    G.setLayer("CYLINDER_LAYER")
    G.setThickness(-25)
    G.circle({400, 100}, 50)
end
```

### Multiple Layers with Different Depths
```lua
function modelMain()
    G = ADekoLib
    
    -- Thick layer (deep cut)
    G.setLayer("DEEP_CUT")
    G.setThickness(-20)
    G.rectangle({100, 100}, {200, 150})
    
    -- Thin layer (shallow cut)
    G.setLayer("SHALLOW_CUT") 
    G.setThickness(-5)
    G.rectangle({250, 100}, {350, 150})
end
```

### Complex Polygons
```lua
function modelMain()
    G = ADekoLib
    
    -- Create a hexagonal pocket
    G.setLayer("HEX_POCKET")
    G.setThickness(-15)
    
    local hexPoints = {}
    local centerX, centerY, radius = 200, 200, 60
    for i = 0, 5 do
        local angle = i * math.pi / 3
        local x = centerX + radius * math.cos(angle)
        local y = centerY + radius * math.sin(angle)
        table.insert(hexPoints, {x, y})
    end
    G.polygon(hexPoints)
end
```

## Technical Details

### Components
- **ZdogCanvas.vue**: The main 3D rendering component using Zdog
- **VisualizationPanel.vue**: Updated to support 3D view mode switching
- **zdog.d.ts**: TypeScript definitions for Zdog library

### Shape Rendering
- **2D Mode**: Shapes render as flat graphics with traditional canvas drawing
- **3D Mode**: Shapes are extruded into 3D with:
  - Configurable depth/thickness
  - Automatic face shading (top, bottom, sides)
  - Layer-based color variations
  - Wireframe option for technical visualization

### Performance
- Zdog is optimized for smooth real-time interaction
- Shapes are grouped by layers for efficient rendering
- Automatic level-of-detail based on zoom level

## Testing

Use the provided test script `test_zdog_3d.lua` to see various 3D shapes and effects:

1. Load the test script in the editor
2. Run the script to generate drawing commands
3. Switch to 3D view mode (Ctrl+2 or click "3D" tab)
4. Interact with the 3D scene using mouse controls

## Keyboard Shortcuts

- `Ctrl+1`: Switch to 2D view
- `Ctrl+2`: Switch to 3D view  
- `Ctrl+3`: Switch to Tools view
- `Ctrl+T`: Quick switch to Tools view

## Future Enhancements

Potential improvements for the 3D visualization:
- Lighting and shadow effects
- Animation support for construction sequences
- Export to 3D formats (OBJ, STL)
- VR/AR viewing modes
- Advanced material rendering
- Physics simulation integration

## Troubleshooting

### Common Issues
1. **3D view not loading**: Ensure Zdog is properly installed (`npm install zdog`)
2. **Performance issues**: Try wireframe mode for complex scenes
3. **TypeScript errors**: Check that zdog.d.ts types are properly imported

### Browser Compatibility
- Modern browsers with Canvas support
- Hardware acceleration recommended for smooth interaction
- WebGL not required (Zdog uses 2D canvas)
