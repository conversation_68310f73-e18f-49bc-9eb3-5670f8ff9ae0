declare module 'zdog' {
  export interface Vector {
    x?: number
    y?: number
    z?: number
  }

  export interface PathCommand extends Vector {
    arc?: [Vector, Vector]
  }

  export interface IllustrationOptions {
    element?: HTMLCanvasElement | SVGElement
    dragRotate?: boolean
    zoom?: number
    rotate?: Vector
    resize?: boolean
    onDragStart?: () => void
    onDragMove?: () => void
    onDragEnd?: () => void
  }

  export interface AnchorOptions {
    addTo?: Anchor
    translate?: Vector
    rotate?: Vector
    scale?: Vector | number
  }

  export interface ShapeOptions extends AnchorOptions {
    path?: (Vector | PathCommand)[]
    stroke?: number
    color?: string
    fill?: boolean
    closed?: boolean
    visible?: boolean
  }

  export interface EllipseOptions extends AnchorOptions {
    diameter?: number
    width?: number
    height?: number
    stroke?: number
    color?: string
    fill?: boolean
  }

  export interface RectOptions extends AnchorOptions {
    width?: number
    height?: number
    stroke?: number
    color?: string
    fill?: boolean
  }

  export interface BoxOptions extends AnchorOptions {
    width?: number
    height?: number
    depth?: number
    stroke?: number
    color?: string
    fill?: boolean
    topFace?: string
    bottomFace?: string
    leftFace?: string
    rightFace?: string
    frontFace?: string
    rearFace?: string
  }

  export interface CylinderOptions extends AnchorOptions {
    diameter?: number
    length?: number
    stroke?: number
    color?: string
    fill?: boolean
  }

  export class Anchor {
    addTo?: Anchor
    translate: Vector
    rotate: Vector
    scale: Vector
    children: Anchor[]
    
    constructor(options?: AnchorOptions)
    copy(options?: AnchorOptions): Anchor
    copyGraph(options?: AnchorOptions): Anchor
    normalizeRotate(): void
    updateGraph(): void
    renderGraphCanvas(ctx: CanvasRenderingContext2D): void
    renderGraphSvg(svg: SVGElement): void
  }

  export class Group extends Anchor {
    constructor(options?: AnchorOptions)
  }

  export class Shape extends Anchor {
    path: (Vector | PathCommand)[]
    stroke: number
    color: string
    fill: boolean
    closed: boolean
    visible: boolean
    
    constructor(options?: ShapeOptions)
  }

  export class Ellipse extends Shape {
    diameter: number
    
    constructor(options?: EllipseOptions)
  }

  export class Rect extends Shape {
    width: number
    height: number
    
    constructor(options?: RectOptions)
  }

  export class Box extends Anchor {
    width: number
    height: number
    depth: number
    
    constructor(options?: BoxOptions)
  }

  export class Cylinder extends Anchor {
    diameter: number
    length: number
    
    constructor(options?: CylinderOptions)
  }

  export class Illustration extends Anchor {
    element: HTMLCanvasElement | SVGElement
    zoom: number
    dragRotate: boolean
    
    constructor(options: IllustrationOptions)
    updateRenderGraph(): void
    setSize(width: number, height: number): void
  }

  export default {
    Illustration,
    Anchor,
    Group,
    Shape,
    Ellipse,
    Rect,
    Box,
    Cylinder
  }
}
