-- Test script for Zdog 3D visualization
-- This script creates various shapes to demonstrate the pseudo 3D capabilities

function modelMain()
    G = ADekoLib
    
    -- Test 1: 3D boxes with different layer thicknesses
    print("Creating 3D boxes...")
    
    -- Thick box
    G.setLayer("THICK_BOX")
    G.setThickness(-30)  -- Negative thickness for cutting depth
    G.rectangle({50, 50}, {250, 200})
    
    -- Medium box
    G.setLayer("MEDIUM_BOX") 
    G.setThickness(-15)
    G.rectangle({300, 50}, {450, 200})
    
    -- Thin box
    G.setLayer("THIN_BOX")
    G.setThickness(-5)
    G.rectangle({500, 50}, {650, 200})
    
    -- Test 2: 3D cylinders with different depths
    print("Creating 3D cylinders...")
    
    -- Deep cylinder
    G.setLayer("DEEP_CYLINDER")
    G.setThickness(-25)
    G.circle({150, 300}, 40)
    
    -- Medium cylinder
    G.setLayer("MED_CYLINDER")
    G.setThickness(-12)
    <PERSON><PERSON>circle({300, 300}, 35)
    
    -- Shallow cylinder
    <PERSON><PERSON>setLayer("SHALLOW_CYLINDER")
    G<PERSON>setThickness(-6)
    <PERSON><PERSON>circle({450, 300}, 30)
    
    -- Test 3: Lines with different layers (will show as 3D when thickness varies)
    print("Creating 3D lines...")
    
    G.setLayer("THICK_LINES")
    G.setThickness(-20)
    G.line({100, 450}, {200, 500})
    G.line({120, 450}, {220, 500})
    G.line({140, 450}, {240, 500})
    
    G.setLayer("THIN_LINES")
    G.setThickness(-8)
    G.line({300, 450}, {400, 500})
    G.line({320, 450}, {420, 500})
    G.line({340, 450}, {440, 500})
    
    -- Test 4: Complex shapes with polygons
    print("Creating complex 3D shapes...")
    
    -- Hexagonal pocket
    G.setLayer("HEX_POCKET")
    G.setThickness(-18)
    local hexPoints = {}
    local centerX, centerY, radius = 150, 600, 50
    for i = 0, 5 do
        local angle = i * math.pi / 3
        local x = centerX + radius * math.cos(angle)
        local y = centerY + radius * math.sin(angle)
        table.insert(hexPoints, {x, y})
    end
    --G.polygon(hexPoints)
    
    -- Star shape
    G.setLayer("STAR_SHAPE")
    G.setThickness(-10)
    local starPoints = {}
    local starCenterX, starCenterY = 400, 600
    for i = 0, 9 do
        local angle = i * math.pi / 5
        local r = (i % 2 == 0) and 40 or 20  -- Alternate between outer and inner radius
        local x = starCenterX + r * math.cos(angle)
        local y = starCenterY + r * math.sin(angle)
        table.insert(starPoints, {x, y})
    end
   -- G.polygon(starPoints)
    
    -- Test 5: Arcs and curves
    print("Creating 3D arcs...")
    
    G.setLayer("ARC_LAYER")
    G.setThickness(-12)
    G.arc({550, 300}, 50, 0, math.pi)  -- Half circle arc
    G.arc({550, 400}, 40, math.pi/4, 3*math.pi/4)  -- Quarter arc
    
    -- Test 6: Text labels (will show as small markers in 3D)
    G.setLayer("LABELS")
    G.setThickness(-2)
    -- Note: If text function exists, uncomment these:
    -- G.text({50, 20}, "3D Boxes")
    -- G.text({150, 280}, "Cylinders") 
    -- G.text({100, 430}, "Lines")
    -- G.text({150, 580}, "Polygons")
    
    -- Instead, create small marker circles for labels
    G.circle({50, 20}, 3)   -- 3D Boxes marker
    G.circle({150, 280}, 3) -- Cylinders marker  
    G.circle({100, 430}, 3) -- Lines marker
    G.circle({150, 580}, 3) -- Polygons marker
    
    print("3D visualization test completed!")
    print("Switch to 3D view to see the pseudo-3D representation")
    print("Use Ctrl+2 to switch to 3D mode, or click the 3D tab")
    print("Different layer thicknesses will show as different extrusion depths")
end

-- Run the main function
require "ADekoDebugMode"
